<template>
  <div>
    <el-form-item prop="username">
      <el-input
        v-model="loginForm.username"
        type="text"
        size="large"
        auto-complete="off"
        placeholder="请输入账户名称"
        maxlength="20"
      >
        <template #prefix>
          <i class="iconfont icon-zhanghao"></i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item class="mt-30" prop="password">
      <el-input
        v-model="loginForm.password"
        type="password"
        size="large"
        auto-complete="off"
        placeholder="请输入登录密码"
        show-password
        maxlength="20"
        @keyup.enter="handleLogin"
      >
        <template #prefix>
          <i class="iconfont icon-denglumima"></i>
        </template>
      </el-input>
    </el-form-item>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps(['loginForm'])
const emit = defineEmits(['update:loginForm','handleLogin'])

const handleLogin = () => {
  emit('handleLogin')
}

</script>