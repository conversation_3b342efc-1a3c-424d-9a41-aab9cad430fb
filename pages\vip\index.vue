<template>
  <div>
    <div class="banner">
      <img src="@/assets/images/vip/bg_image.png" alt="" class="banner-bg" />
      <div class="banner-content">
        <img src="@/assets/images/vip/bg_title.png" alt="" class="banner-title" />
        <div class="main-wrap tabs">
          <div class="app-tabs">
            <div class="flex flex-ai-center">
              <div
                @click="handleTab(index)"
                v-for="(item, index) in tabsList"
                :key="index"
                class="app-tabs-item"
                :class="{ 'app-tabs-active': index == activeTab }"
              >
                {{ item }}
              </div>
            </div>
          </div>
          <div v-show="activeTab == 0" class="membership">
            <div class="membership-item">
              <img src="@/assets/images/vip/content.png" alt="" class="membership-item-img" />
              <div class="h-title f-20">服务项目</div>
              <div v-for="(item, index) in servicesAvailable" :key="index" class="membership-item-subitem">{{ item.name }}</div>
            </div>
            <div class="membership-item">
              <div class="membership-item-subitem gradient-gay f-bold f-16">非会员用户</div>
              <div class="membership-item-subitem h-154 flex flex-center f-bold f-18">免费</div>
              <VipServicesCard :row="freeMember" />
            </div>
            <div class="membership-item">
              <div class="membership-item-subitem gradient-blue f-bold f-16">个人会员</div>
              <div v-for="(item, index) in individualMember" :key="index" class="membership-item hover-person">
                <img src="@/assets/images/vip/person.png" alt="" class="membership-item-hover" />
                <VipPriceCard :row="item" @pay="handlePay" />
                <VipServicesCard :row="item.package ?? []" />
              </div>
            </div>
            <div class="membership-item">
              <div class="membership-item-subitem gradient-blue f-bold f-16 btrr-5">企业会员</div>
              <div class="flex">
                <div v-for="(item, index) in corporateMember" :key="index" class="membership-item flex-1 hover-firm hover-person">
                  <img src="@/assets/images/vip/enterprise.png" alt="" class="membership-item-hover" />
                  <VipPriceCard :row="item" @pay="handlePay" class="pl-0 flex flex-center bt-none" />
                  <VipServicesCard :row="item.package ?? []" class="pl-0" />
                </div>
              </div>
            </div>
          </div>
          <VipBenefitPackage v-show="activeTab == 1" :benefitPackage="benefitPackage" @pay="handlePay" />
        </div>
      </div>
    </div>
    <VipIntroduceProblems />
    <VipPayDialog v-if="payVisible" v-model:visible="payVisible" :selectItem="selectItem" />
  </div>
</template>

<script setup lang="ts">
  useHead({
    title: '注册会员_开通会员_VIP会员_权益包会员_标信查平台',
    meta: [
      { name: 'keywords', content: '注册会员，开通会员，标信查VIP会员，标信查权益包会员' },
      {
        name: 'description',
        content:
          '标信查平台致力于为用户提供最全面、最专业、最便捷的标准查询、管理、学习及应用服务。为了更好地满足广大用户的需求，特别推出了个人会员与企业会员两种会员服务、并根据不同会员等级提供丰富多样的会员权益，欢迎广大用户选择购买。',
      },
    ],
  });

  import type { IServiceItem, IMemberPackage, IBenefitPackage, IBenefitPackageItem } from '@/types/index';

  const tabsList = ref<string[]>(['VIP会员', '权益包会员']);
  const activeTab = ref<number>(0);
  const servicesAvailable = ref<IServiceItem[]>([
    { id: 100, name: '标准检索' },
    { id: 101, name: '国际国外标准检索' },
    { id: 102, name: '数字标准' },
    { id: 103, name: '标准比对' },
    { id: 104, name: '标准托管' },
    { id: 105, name: '标准查新' },
    { id: 106, name: '行业标准体系' },
    { id: 107, name: '标准专题' },
    { id: 108, name: '标准知识' },
    { id: 109, name: '标准解析器' },
    { id: 110, name: '数字标准编辑器' },
    { id: 111, name: '标准问答' },
    { id: 112, name: '多设备登录' },
    { id: 113, name: '成员数量' },
  ]);
  const freeMember = ref<IServiceItem[]>([
    { id: 100, number: '有', name: '标准检索' },
    { id: 101, number: '有', name: '国际国外标准检索' },
    { id: 102, number: '前三章', name: '数字标准' },
    { id: 103, number: '无', name: '标准比对' },
    { id: 104, number: '3条', name: '标准托管' },
    { id: 105, number: '8次', name: '标准查新' },
    { id: 106, number: '有', name: '行业标准体系' },
    { id: 107, number: '有', name: '标准专题' },
    { id: 108, number: '有', name: '标准知识' },
    { id: 109, number: '30页', name: '标准解析器' },
    { id: 110, number: '无', name: '数字标准编辑器' },
    { id: 111, number: '无', name: '标准问答' },
    { id: 112, number: '1台', name: '多设备登录' },
    { id: 113, number: '无', name: '成员数量' },
  ]);
  const individualMember = ref<IMemberPackage[]>([
    {
      id: 3,
      originalPrice: 600,
      discount: 300,
      type: 'annual',
      packageType: 1,
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '100条', name: '标准托管' },
        { id: 105, number: '3000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '1500页', name: '标准解析器' },
        { id: 110, number: '无', name: '数字标准编辑器' },
        { id: 111, number: '10个会话', name: '标准问答' },
        { id: 112, number: '1台', name: '多设备登录' },
        { id: 113, number: '无', name: '成员数量' },
      ],
    },
  ]);
  const corporateMember = ref<IMemberPackage[]>([
    {
      id: 4,
      originalPrice: 1200,
      discount: 400,
      type: 'annual',
      packageType: 2,
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '200条', name: '标准托管' },
        { id: 105, number: '8000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '4000页', name: '标准解析器' },
        { id: 110, number: '2项目', name: '数字标准编辑器' },
        { id: 111, number: '10个会话', name: '标准问答' },
        { id: 112, number: '5台', name: '多设备登录' },
        { id: 113, number: '10个', name: '成员数量' },
      ],
    },
    {
      id: 5,
      originalPrice: 2400,
      discount: 400,
      type: 'annual',
      packageType: 2,
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '300条', name: '标准托管' },
        { id: 105, number: '12000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '10000页', name: '标准解析器' },
        { id: 110, number: '5项目', name: '数字标准编辑器' },
        { id: 111, number: '20个会话', name: '标准问答' },
        { id: 112, number: '10台', name: '多设备登录' },
        { id: 113, number: '20个', name: '成员数量' },
      ],
    },
    {
      id: 6,
      originalPrice: 3600,
      discount: 400,
      type: 'annual',
      packageType: 2,
      package: [
        { id: 100, number: '有', name: '标准检索' },
        { id: 101, number: '有', name: '国际国外标准检索' },
        { id: 102, number: '不限', name: '数字标准' },
        { id: 103, number: '有', name: '标准比对' },
        { id: 104, number: '500条', name: '标准托管' },
        { id: 105, number: '18000次', name: '标准查新' },
        { id: 106, number: '有', name: '行业标准体系' },
        { id: 107, number: '有', name: '标准专题' },
        { id: 108, number: '有', name: '标准知识' },
        { id: 109, number: '不限', name: '标准解析器' },
        { id: 110, number: '10项目', name: '数字标准编辑器' },
        { id: 111, number: '30个会话', name: '标准问答' },
        { id: 112, number: '20台', name: '多设备登录' },
        { id: 113, number: '30个', name: '成员数量' },
      ],
    },
  ]);
  const benefitPackage = ref<IBenefitPackage[]>([
    {
      id: 30000,
      title: '标准查新',
      introduce: '标准查新权益包体验优质服务标准查新权益包体验优质服务',
      packageType: 3,
      package: [
        { id: 1, originalPrice: 60, discount: 40, indate: '1', type: 'annual', number: '600次', name: '标准查新' },
        { id: 2, originalPrice: 70, discount: 40, indate: '1', type: 'annual', number: '1000次', name: '标准查新' },
        { id: 3, originalPrice: 165, discount: 40, indate: '1', type: 'annual', number: '5000次', name: '标准查新' },
        { id: 4, originalPrice: 239, discount: 40, indate: '1', type: 'annual', number: '10000次', name: '标准查新' },
      ],
    },
  ]);
  const payVisible = ref<boolean>(false);
  const selectItem = ref<IMemberPackage | IBenefitPackageItem>({} as IMemberPackage | IBenefitPackageItem);

  const handleTab = (index: number) => {
    activeTab.value = index;
  };

  const handlePay = (row: IMemberPackage) => {
    selectItem.value = row;
    payVisible.value = true;
  };
</script>

<style lang="scss" scoped>
  .banner {
    position: relative;
    width: 100%;

    &-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      max-height: 1146px;
      width: 100%;
    }

    &-content {
      position: relative;
      padding-top: 140px;
    }

    &-title {
      display: block;
      margin: 0 auto;
      height: 47px;
    }

    .tabs {
      margin-top: 37px;
      overflow: hidden;
      border-image: linear-gradient(0deg, #ffffff, #c2ddff) 10 10;

      .app-tabs {
        height: 80px;
        background: #e8f3ff;
        padding: 0 25px;
        box-sizing: border-box;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-top: 1px solid #c2ddff;
        border-left: 1px solid #c2ddff;
        border-right: 1px solid #c2ddff;

        &-item {
          font-size: 22px;
          font-weight: bold;
          color: #333;

          &:hover {
            color: $primary-color;
          }

          &:nth-child(n + 2) {
            margin-left: 75px;
          }
        }
      }
    }

    .membership {
      display: flex;
      padding: 35px 25px;
      box-sizing: border-box;
      background: #fff;
      border-left: 1px solid;
      border-right: 1px solid;
      border-image: linear-gradient(to bottom, #c2ddff, #ffffff) 1;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;

      &-item {
        &:first-child .membership-item-subitem {
          background-color: #f8fbff;
        }

        &:nth-child(n + 2) .membership-item-subitem {
          border-left: none !important;
        }

        &:nth-child(2),
        &:nth-child(3) {
          width: 180px;
        }

        &:nth-child(4) {
          flex: 1;
        }

        &-img {
          display: block;
          height: 140px;
        }

        &-subitem {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: #333;
          height: 54px;
          border: 1px solid #ddecf8;
          box-sizing: border-box;

          &:not(:first-child) {
            border-top: none !important;
          }
        }

        &-hover {
          height: 60px;
          position: absolute;
          top: -30px;
          left: calc((100% - 60px) / 2);
        }
      }
    }
  }

  .h-title {
    height: 68px;
    line-height: 68px;
    background-color: #f8fbff;
    padding-left: 25px;
    border-left: 1px solid #ddecf8;
    border-right: 1px solid #ddecf8;
    border-bottom: 1px solid #ddecf8;
    box-sizing: border-box;

    &::before {
      content: '';
      position: absolute;
      top: calc(50% - 10px);
      left: 13px;
      width: 4px;
      height: 20px;
      background: $primary-color;
    }
  }

  .gradient {
    &-gay {
      background: linear-gradient(0deg, #ffffff 0%, #ececec 100%);
    }

    &-blue {
      background: linear-gradient(0deg, #ffffff 0%, #dbe8ff 100%);
    }
  }

  .h-154 {
    height: 154px !important;
    line-height: normal !important;
    text-align: center;
  }

  .pl-0 {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  .bt-none {
    border-top: none !important;
  }

  .membership-item-hover {
    display: none;
  }

  .hover-person {
    position: relative;
    transform: scale(1);
    transition: transform 0.3s ease;
    z-index: 1;

    &:before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #ae00ff, #045cff);
      z-index: -1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:after {
      content: '';
      position: absolute;
      inset: 0;
      background: #fff;
      z-index: -1;
    }

    &:hover {
      transform: scale(1.01);
      z-index: 3;

      &:before {
        opacity: 1;
      }

      .membership-item-hover {
        display: block !important;
      }

      .membership-item-subitem {
        background: #fff;
        border: none;
      }
    }
  }

  .btrr-5 {
    border-top-right-radius: 5px;
    overflow: hidden;
  }
</style>
