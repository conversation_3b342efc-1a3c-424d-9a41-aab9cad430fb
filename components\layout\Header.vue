<template>
  <div class="header-wrap" :class="{ opaqueHeader: isBgWhite }">
    <div class="header-container main-wrap">
      <div class="header-left">
        <NuxtLink to="/">
          <img src="@/assets/images/layout/logo.png" alt="" />
        </NuxtLink>
        <!-- 应用 -->
        <LayoutHeaderApplication />
        <!-- 解决方案 -->
        <LayoutHeaderSolution />
        <NuxtLink to="/vip">
          <div class="header-title">会员</div>
        </NuxtLink>
        <div @click="handleBuild" class="ml-50 pointer">数据服务</div>
        <!-- 支持与服务 -->
        <LayoutHeaderSupport />
      </div>
      <div class="header-right">
        <template v-if="userStore.token">
          <!-- 通知信息 -->
          <LayoutHeaderNotice />
          <!-- 用户信息 -->
          <LayoutHeaderUser />
        </template>
        <template v-else>
          <NuxtLink :to="`/login?redirect=${route.fullPath}`" class="header-login">登录</NuxtLink>
          <NuxtLink to="/register" class="header-register">立即注册</NuxtLink>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { useWindowScroll } from '@vueuse/core';
  import { useUserStore } from '@/store/userStore';

  const { $modal } = useNuxtApp();
  const userStore = useUserStore();
  const route = useRoute();

  const handleBuild = () => {
    $modal.msgWarning('正在建设中，敬请期待');
  };
  const isBgWhite = ref(false);

  onMounted(() => {
    const { y } = useWindowScroll();

    watchEffect(() => {
      isBgWhite.value = y.value > 0;
    });
  });

  if (userStore.token) userStore.getUserInfo();
</script>

<style lang="scss" scoped>
  .header-title {
    cursor: pointer;
    color: #333333;
    margin-left: 50px;

    &:hover {
      color: $primary-color;
    }
  }
</style>
