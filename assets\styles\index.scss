@import './iconfont.scss';
@import './normalize.scss';
@import './bxc.scss';

html,
body {
  min-width: 1200px;
  margin: 0 auto;

  &::-webkit-scrollbar {
    width: 0px !important;
  }
}

a {
  text-decoration: none;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.app-richtext {
  font-size: 14px;
  color: #333;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  line-height: 25px;
}

// flex
.flex {
  display: flex;
}

.flex-sb {
  justify-content: space-between;
}

.flex-sa {
  justify-content: space-around;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-jc-center {
  justify-content: center;
}

.flex-jc-start {
  justify-content: flex-start;
}

.flex-jc-end {
  justify-content: flex-end;
}

.flex-ai-center {
  align-items: center;
}

.flex-ai-start {
  align-items: flex-start;
}
.flex-ac-sb {
  align-content: space-between;
}
.flex-1 {
  flex: 1;
}
.flex-shrink {
  flex-shrink: 0;
}

.flex-content-start {
  align-content: flex-start;
}

.flex-self-start {
  align-self: flex-start;
}

.flex-self-end {
  align-self: flex-end;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 溢出隐藏 */
.overflow-hidden {
  overflow: hidden;
}

/* 单行文本溢出打点 */
.overflow-ellipsis {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1.3;
}

// 两行文本溢处打点
.overflow-two-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}

// 三行行文本溢处打点
.overflow-three-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}

// 字体
.f-12 {
  font-size: 12px !important;
}

.f-14 {
  font-size: 14px !important;
}

.f-16 {
  font-size: 16px !important;
}

.f-18 {
  font-size: 18px !important;
}

.f-20 {
  font-size: 20px !important;
}

.f-22 {
  font-size: 22px !important;
}

.f-24 {
  font-size: 24px !important;
}

.f-25 {
  font-size: 25px !important;
}

.f-26 {
  font-size: 26px !important;
}

.f-28 {
  font-size: 28px !important;
}

.f-30 {
  font-size: 30px !important;
}
.f-32 {
  font-size: 32px !important;
}

.f-36 {
  font-size: 36px !important;
}

.fw-500 {
  font-weight: 500 !important;
}

.f-bold {
  font-weight: bold !important;
}

.lh-20 {
  line-height: 20px !important;
}

.lh-22 {
  line-height: 22px !important;
}

.lh-24 {
  line-height: 24px !important;
}

.lh-26 {
  line-height: 26px !important;
}

// 背景色
.bgc-ff {
  background-color: #ffffff !important;
}
// 背景色
.bgc-00 {
  background-color: #000000 !important;
}

.bgc-f5 {
  background-color: #f5f5f5 !important;
}

.bgc-f7 {
  background-color: #f7f7f7 !important;
}

.bgc-e6 {
  background-color: #e6e6e6 !important;
}

.bgc-primary {
  background-color: $primary-color !important;
}

.bgc-0069EA {
  background-color: #0069ea !important;
}

// 颜色
.c-ff {
  color: #ffffff !important;
}

.c-22 {
  color: #222222 !important;
}

.c-33 {
  color: #333333 !important;
}

.c-55 {
  color: #555555 !important;
}

.c-66 {
  color: #666666 !important;
}

.c-88 {
  color: #888888 !important;
}

.c-99 {
  color: #999999 !important;
}

.c-e4 {
  color: #e40000;
}

.c-d7 {
  color: #d7d7d7;
}

.c-fb {
  color: #fb0000;
}

.c-ff0000 {
  color: #ff0000 !important;
}

.c-0069EA {
  color: #0069ea !important;
}

.c-0096FF {
  color: #0096ff !important;
}

.c-blue {
  color: #0194f6 !important;
}

.c-primary {
  color: $primary-color !important;
}

// padding
.pb-5 {
  padding-bottom: 5px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pl-15 {
  padding-left: 15px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pl-25 {
  padding-left: 25px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pr-15 {
  padding-right: 15px !important;
}

.pr-30 {
  padding-right: 30px !important;
}
.plr-20 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}
.plr-25 {
  padding-left: 25px !important;
  padding-right: 25px !important;
}
.pt-15 {
  padding-top: 15px !important;
}
.pt-20 {
  padding-top: 20px !important;
}
.pt-30 {
  padding-top: 30px !important;
}
.pt-40 {
  padding-top: 40px !important;
}
.pt-50 {
  padding-top: 50px !important;
}
.pt-60 {
  padding-top: 60px !important;
}
// margin
.ml-5 {
  margin-left: 5px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.ml-15 {
  margin-left: 15px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.ml-25 {
  margin-left: 25px !important;
}

.ml-30 {
  margin-left: 30px !important;
}
.ml-40 {
  margin-left: 40px !important;
}
.ml-50 {
  margin-left: 50px !important;
}
.ml-60 {
  margin-left: 60px !important;
}
.ml-70 {
  margin-left: 70px !important;
}
.ml-80 {
  margin-left: 80px !important;
}

.mt-5 {
  margin-top: 5px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-25 {
  margin-top: 25px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-35 {
  margin-top: 35px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mr-25 {
  margin-right: 25px !important;
}

.mr-30 {
  margin-right: 30px !important;
}
.mr-40 {
  margin-right: 40px !important;
}
.mr-50 {
  margin-right: 50px !important;
}
.mr-60 {
  margin-right: 60px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}
.mb-20 {
  margin-bottom: 20px !important;
}
.mb-30 {
  margin-bottom: 30px !important;
}
.mb-40 {
  margin-bottom: 40px !important;
}
.mb-50 {
  margin-bottom: 50px !important;
}

.mg-auto {
  margin: auto !important;
}

.mg0auto {
  margin: 0 auto !important;
}

// 宽度
.w-600 {
  width: 600px !important;
}
.w-530 {
  width: 530px !important;
}
.w-100vw {
  width: 100% !important;
}
.w-230 {
  width: 230px !important;
}

// 高度
.h-52 {
  height: 52px !important;
}
.h-46 {
  height: 46px !important;
}
.h-44 {
  height: 44px !important;
}
.h-495 {
  height: 495px !important;
}
.h-54 {
  height: 54px !important;
}

.pointer {
  cursor: pointer;
}

.br-5 {
  border-radius: 5px;
}

// 动效
.img-scale {
  display: block;
  transition-duration: 0.4s;
  &:hover {
    transform: scale(1.1);
    transition: all 0.6s ease-in-out;
  }
}
.move-up {
  transition-duration: 0.4s;
  &:hover {
    transform: translateY(-5px);
    transition: all 0.6s ease-in-out;
  }
}
.tip-box {
  background: #f8f9fb;
  border-radius: 3px;
  padding: 10px 20px;
  color: #888888;
  line-height: 20px;
  .mobile {
    color: #333333;
    font-weight: bold;
  }
}
// 标签
.h-title {
  font-size: 18px;
  color: #333333;
  position: relative;
  padding-left: 10px;
  box-sizing: border-box;
  font-weight: bold;

  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 10px);
    left: 0px;
    width: 4px;
    height: 20px;
    background: $primary-color;
  }
}
.f-hover {
  &:hover {
    color: $primary-color !important;
  }
}
.text-underline {
  text-decoration: underline;
}

.fit-cover {
  object-fit: cover;
}

// el-table
.el-table th.el-table__cell.is-leaf {
  height: 48px;
  color: #333;
  background-color: #f6f7f9;
}

.el-table .cell {
  font-size: 14px !important;
  color: #333333 !important;
  font-weight: 400 !important;
}

.el-table .cell.el-tooltip {
  font-size: 14px !important;
  color: #333333 !important;
}

.el-table .cell {
  text-align: center !important;
}

.el-table__cell {
  height: 62px;
}

// el-form
.el-form--label-top .el-form-item {
  margin-bottom: 18px !important;
  margin-right: 0px !important;
}

.el-form-item__label {
  font-size: 14px !important;
  color: #333333 !important;
  padding-bottom: 0 !important;
}

// el-dialog
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  padding: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.el-dialog__body {
  padding: 20px 20px !important;
  box-sizing: border-box !important;
  max-height: calc(100vh - 150px) !important;
  max-width: 100% !important;
  overflow-y: auto !important;
}

.el-dialog__body::-webkit-scrollbar {
  width: 7px !important;
  height: 10px !important;
}

.el-dialog__body::-webkit-scrollbar-thumb {
  background-color: #dedfe0 !important;
  border-radius: 10px !important;
}

.el-dialog__header {
  margin-right: 0 !important;
  padding: 16px 16px 0 16px;
  box-sizing: border-box;
  border-bottom: 1px solid #e5e8ef;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 100px !important;
}

.el-dialog__title {
  font-size: 20px !important;
  font-weight: bold;
}

.el-dialog__close {
  color: #8f9bb3 !important;
  font-size: 28px !important;
}

.el-dialog:not(.is-fullscreen) {
  border-radius: 5px;
}

.el-dialog__headerbtn {
  top: 6px !important;
}

.el-dialog__footer {
  width: 100%;
  padding: 5px 16px 25px 16px !important;
  box-sizing: border-box !important;
  margin: 0 auto !important;
}
.el-dialog__footer .el-button.is-plain {
  min-width: 84px;
  height: 38px;
}
.el-dialog__footer .el-button--primary {
  min-width: 84px;
  height: 38px;
  background-color: $primary-color;
  border: none;
}

.el-dialog__footer .el-button--primary.is-plain {
  color: $primary-color;
  min-width: 84px;
  height: 38px;
  background-color: #fff;
  border: 1px solid $primary-color;
}
// .el-drawer
.el-drawer {
  .el-drawer__header {
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    border-bottom: 1px solid #e5e8ef;
    height: 58px;
    line-height: 58px;
    padding: 0 10px 0px 20px;
    box-sizing: border-box;
    margin-bottom: 0px;
  }
  .el-drawer__body {
    padding: 0 20px 20px 20px !important;
  }
  .el-drawer__body::-webkit-scrollbar {
    width: 7px !important;
    height: 10px !important;
  }

  .el-drawer__body::-webkit-scrollbar-thumb {
    background-color: #dedfe0 !important;
    border-radius: 10px !important;
  }
}
// el-tooltip
.el-tooltip__popper.is-dark {
  max-width: 200px !important;
}

// el-empty
.el-empty {
  height: 100% !important;
}
.scroll-style::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.scroll-style::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
  -webkit-box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
  background: #d5d5d5;
}

.scroll-style::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
  -webkit-box-shadow: inset 0 0 5px rgba(218, 237, 255, 0.5);
  border-radius: 8px;
  background: #eeeeee;
}

.form-inline {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;

  .el-form-item {
    width: 46%;
    display: flex;
    margin-right: 0px !important;

    .el-input,
    .el-select,
    .el-cascader,
    .el-textarea {
      width: 100% !important;
    }
  }

  .half-column {
    width: 46%;
  }

  .one-column {
    width: 100%;
  }
}

.status {
  &-tag {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    padding: 0 10px;
    text-align: center;
    border-radius: 3px;
  }

  &-045CFE {
    color: #045cfe;
  }

  &-bg045CFE {
    background-color: #dbe8ff;
  }

  &-04AE00 {
    color: #04ae00;
  }

  &-bg04AE00 {
    background-color: #e5fae4;
  }

  &-A800FF {
    color: #a800ff;
  }

  &-bgA800FF {
    background-color: #efdeff;
  }

  &-FFB400 {
    color: #ffb400;
  }

  &-bgFFB400 {
    background-color: #fff4d8;
  }

  &-FF6600 {
    color: #ff6600;
  }

  &-bgFFE6D6 {
    background-color: #ffe6d6;
  }

  &-00AEFF {
    color: #00aeff;
  }

  &-bg00AEFF {
    background-color: #d8ebff;
  }

  &-FF0000 {
    color: #ff0000;
  }

  &-bgFF0000 {
    background-color: #ffd5d5;
  }

  &-999999 {
    color: #999999;
  }

  &-bg999999 {
    background-color: #e7e7e7;
  }

  &-00C7F2 {
    color: #00c7f2;
  }

  &-bg00C7F2 {
    background-color: #dbf9ff;
  }
}

.el-button:focus-visible {
  outline: none !important;
}

.app-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-item {
    position: relative;
    padding: 10px 0;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: -1.5px;
      left: calc((100% - 75%) / 2);
      width: 75%;
      height: 3px;
      background-color: transparent;
      transition: all 0.3s ease;
      border-radius: 2px;
    }
  }

  &-active {
    color: $primary-color !important;
    font-weight: 600 !important;

    &::after {
      background-color: $primary-color !important;
    }
  }
}

.line-through {
  text-decoration: line-through;
}
